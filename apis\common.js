import { $get, $post, getBaseUrl } from '@/utils/request.js'
import queryParams from '@/utils/queryParams'

// 发送验证码
export const sendSmsCode = (data = {}) => {
  return $post({
    url: '/send?phone=' + data.phone,
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 提交信息
export const saveLoan = (data = {}) => {
  return $post({
    url: '/saveLoan',
    data,
    isEncrypt: true,
    deEncrypt: false
  })
}

// ip解析当前地址
export const getIpArea = (data = {}) => {
  return $get({
    url: '/v2/getIpArea',
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取app信息
export const getAppInfo = (data) => {
  return $get({
    data,
    url: '/getAppInfo',
    isEncrypt: false,
    deEncrypt: false
  })
}

// 下载按钮点击统计
export const downloadCount = (data) => {
  return $get({
    url: '/download/click',
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取协议列表
export const getProtocolList = (data = {}) => {
  return $get({
    url: '/protocol/list',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取协议信息
export const getProtocolDetail = (data = {}) => {
  return $get({
    url: '/protocol/query/' + data.protocolId,
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}
// 根据渠道号获取协议
export const getProtocolListById = (data = {}) => {
  return $get({
    url: `/protocol/list/${data}`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 多产品申请
export const applyProducts = (data = {}) => {
  return $post({
    url: '/v2/applyProducts',
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取当前渠道模板型号
export const getTemplateModel = (data = {}) => {
  return $get({
    url: '/temp/verify/' + data.channelId,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 首页行为记录
export const saveRecord = (data = {}) => {
  return $post({
    url: '/behavior/record/addRecord',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请线上产品
export const applyOnlineProduct = (data = {}) => {
  return $post({
    url: '/v2/applyOnlineProduct',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// v14 获取字典信息
export const getV14DictData = (data = {}) => {
  return $get({
    url: '/dict/getH5V5Data',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 广告
export const getAdInfo = (data = {}) => {
  return $get({
    url: '/dc/register/productDC',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 埋点统计
export const addPoint = (data = {}) => {
  return $post({
    url: '/point/addPoint',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

//  匹配
export const eMatch = (data = {}) => {
  return $post({
    url: '/v5/matching/v3',
    isEncrypt: true,
    deEncrypt: false,
    data
  })
}

// 匹配
export const qWMatch = (data = {}) => {
  return $post({
    url: '/v6/download/match/v4',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

export const fMatch = (data = {}) => {
  return $post({
    url: '/v6/download/match/v3',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

export const hMatch = (data = {}) => {
  return $post({
    url: '/v6/download/match/v5',
    isEncrypt: true,
    deEncrypt: false,
    data
  })
}

export const matchV6 = (data = {}) => {
  return $post({
    url: '/v6/download/match/v6',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

export const sequencePro = (data = {}) => {
  return $get({
    url: '/v6/sequence',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取半api产品的链接，参数中的 limit 为总共想要匹配的产品数量，但接口每次只返回一条
export const getHalfApiProductUrl = (data = {}) => {
  return $post({
    url: '/half/api/match',
    isEncrypt: true,
    deEncrypt: false,
    data
  })
}

// 根据邀请码获取渠道号和模板
export const fetchChannelAndTemplate = (data = {}) => {
  return $get({
    url: `/temp/verify/${data.invite}`,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 匹配线上产品
export const fetchOnlineProduct = (data = {}) => {
  return $post({
    url: '/sms/online/match',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请线上产品
export const applyOnlineProductV2 = (data = {}) => {
  return $post({
    url: '/sms/online/apply',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// uv统计
export const reportUV = (data = {}) => {
  return $get({
    url: '/user/8a848a',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取协议详情
export const getProtocolDetailV2 = (data = {}) => {
  return $get({
    url: `/sms/query/${data.protocolId}`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取短信链接 企微的跳转链接
export const fetchWechatLink = (data = {}) => {
  return $post({
    url: '/sms/v3/qw/link/match',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取短信链接 企微的跳转链接 接入匹配
export const fetchWechatLink2 = (data = {}) => {
  return $post({
    url: '/sms/v3/new/qw/link/match',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取手机号申请进度
export const getPhoneApplyProgress = (data = {}) => {
  return $get({
    url: `/user/getProgress/${data.phone}`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取短信链接 企微的跳转链接 分配
export const fetchWechatLinkAssign = (data = {}) => {
  return $post({
    url: '/sms/v3/assign/qw',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取短信链接 企微的跳转链接 V5版本
export const fetchWechatLinkV5 = (data = {}) => {
  return $post({
    url: '/sms/v5/qw/link/match',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取半API进度
export const getHalfApiProgress = (data = {}) => {
  return $get({
    url: '/form/halfProgress',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取半API公众号链接
export const getWeChatOfficialLink = (data = {}) => {
  return $get({
    url: '/half/getWeChatOfficialLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 贷超是否自动申请
export const getDcAutoFlag = (data = {}) => {
  return $get({
    url: '/temp/dcAutoFlag',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 半API匹配
export const fetchHalfMatching = (data = {}) => {
  return $post({
    url: '/half/matching',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取注册用户表单信息
export const getRegisterUserFormInfo = (data = {}) => {
  return $get({
    url: '/ld/getRegisterUserFormInfo',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取申请线上产品的链接
export const getApplyOnlineProductLink = (data = {}) => {
  return `${getBaseUrl()}/form/applyOnline/href${queryParams(data)}`
}

// 获取短信配置信息
export const getTemplateConfig = (data = {}) => {
  return $get({
    url: `/temp/config/${data.channelId}`,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 匹配线上产品
export const matchOnlineProduct = (data = {}) => {
  return $post({
    url: '/official/form/match/wechat',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 保存资质信息
export const saveAptitude = (data = {}) => {
  return $post({
    url: '/online/form/save/aptitude',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 在线产品匹配
export const matchingOnlineProduct = (data = {}) => {
  return $post({
    url: '/online/form/matching/online',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配产品
export const matchProduct = (data = {}) => {
  return $post({
    url: '/official/form/ld/match/product',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 用户进入公众号页面计数
export const reportBurialPoint = (data = {}) => {
  return $post({
    url: '/user/burialPoint',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取微信小程序链接
export const getWechatMiniProgramLink = (data = {}) => {
  return $get({
    url: '/wechat/applet/urlLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取微信小程序 URL Scheme
export const getWechatAppletUrl = (data = {}) => {
  return $post({
    url: '/wechat/applet/url',
    isEncrypt: false, // 根据实际情况调整是否加密
    deEncrypt: false, // 根据实际情况调整是否解密
    data
  })
}

// 获取公众号关注链接
export const getWechatPubUrl = (consumerId) => {
  return $get({
    url: `/wechat/applet/pubUrl/${consumerId}`,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取微信小程序公众号配置
export const getWechatPubConf = (fid) => {
  return $get({
    url: `/wechat/applet/pubconf/${fid}`,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取微信小程序短链接重定向URL
export const getWechatShortRealLink = (data = {}) => {
  return $get({
    url: '/wechat/applet/shortRealLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}
// 获取资质信息补全字段
export const fetchAptitudeRestock = (data = {}) => {
  return $get({
    url: '/online/form/aptitudeRestock',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

export const searchUserAptitude = (params) => {
  return $get({
    url: '/txxj/qw/search',
    isEncrypt: false,
    deEncrypt: false,
    data: params
  })
}

/**
 * 获取需要填写的资质
 * @param {Object} data - 请求参数
 * @param {string} data.channelId - 渠道ID
 * @returns {Promise<string[]>} 返回需要填写的资质列表，如 ["age","sesamId"]，默认返回空数组 []
 */
export const getNeedFillAptitude = (data = {}) => {
  return $get({
    url: '/redress/process',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

/**
 * 更新用户资质
 * @param {Object} data - 请求参数
 * @param {string} data.uid - 用户ID
 * @param {number} data.overdueId - 信用情况，例如：161
 * @param {string} data.sesameId - 芝麻分，例如：115
 * @param {number} data.age - 年龄，例如：25
 * @returns {Promise} 返回更新结果
 */
export const updateUserAptitude = (data = {}) => {
  return $post({
    url: '/redress/supplement',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

/**
 * 根据城市获取机构列表
 * @param {Object} data - 请求参数
 * @param {string} data.cityCode - 城市代码
 * @returns {Promise<Object[]>} 返回机构列表
 */
export const getInstitutionByCity = (data = {}) => {
  return $get({
    url: `/protocol/queryInstitutionByCity`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

/**
 * 判断贷超返回是否使用a标签跳转
 * @param {Object} data - 请求参数
 * @param {string} data.channelId - 渠道ID
 * @param {string} data.consumerId - 用户ID
 * @returns {Promise<number>} 返回1表示使用a标签跳转,2表示不使用
 */
export const checkHrefJump = (data = {}) => {
  return $get({
    url: '/temp/hrefJump',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

/**
 * 申请线上产品href跳转
 * @param {Object} data - 请求参数
 * @param {string} data.consumerId - 用户ID
 * @param {string} data.redirectUrl - 重定向URL
 * @returns {string} 返回跳转链接
 */
export const applyOnlineProductHref = (data = {}) => {
  return `${getBaseUrl()}/form/backApplyOnline/href${queryParams(data)}`
}

// 短链获取真实地址
export const getShortLink = (data = {}) => {
  return $get({
    url: '/outbound/shortLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 短链解析获取真实地址
export const getShortRealLink = (data = {}) => {
  return $get({
    url: '/ld/shortRealLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请产品
export const outboundApplyProduct = (data = {}) => {
  return $post({
    url: '/outbound/apply',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取贷超产品链接
export const getDcProductLink = (data = {}) => {
  return $post({
    url: '/form/matchLoan/apply',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取联登链接
export const getAptitudeLink = (data = {}) => {
  return $get({
    url: '/temp/aptitudeLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}
