<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <scroll-view
      class="page-content"
      scroll-y="true"
      :scroll-into-view="scrollIntoView"
      scroll-with-animation="true"
      @scrolltolower="scrollIntoView = ''"
    >
      <view class="header">
        <view class="header-title">
          <view class="header-title-text">还差一步，即可获取额度</view>
          <view class="header-title-subtext">最高可借200,000元</view>
        </view>
        <view class="tag">
          <view class="tag-number">1000</view>
          <view class="tag-gap-text">人</view>
          已放款
        </view>
      </view>

      <view class="city" v-if="isVisible('city')">
        <view class="city-item">
          <view class="city-label">所在城市</view>
          <view class="city-value">
            <picker
              range-key="name"
              mode="multiSelector"
              :value="form.city"
              :range="areaData"
              @change="regionChange"
              @columnchange="regionColChange"
            >
              <view style="display: flex; align-items: center">
                <image
                  class="city-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/b1eb7edc30a94075bcbcbb5f279a0306.png"
                />
                <view v-if="form.cityName" class="city-name">{{ form.cityName }}</view>
                <view v-else class="placeholder">请选择城市</view>
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </picker>
          </view>
        </view>
        <view class="city-tips">*请务必选择您当前长期居住的城市，否则将影响借款结果</view>
      </view>

      <view
        class="identity card"
        :class="{ activeForm: activeForm === 'identity' }"
        @click="setActiveForm('identity')"
        v-if="isVisible('name') || isVisible('age') || isVisible('sex')"
      >
        <view class="card-header">
          <view class="card-header-label">
            <image
              class="card-header-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/e23d62c7182640168ca938323d410a04.png"
            ></image>
            <view>身份信息</view>
          </view>
          <uni-icons
            :type="activeForm === 'identity' ? 'top' : 'bottom'"
            size="40rpx"
            color="#999"
          ></uni-icons>
        </view>

        <view class="identity-form" v-if="activeForm === 'identity'">
          <template v-if="isVisible('name')">
            <view class="identity-form-item form-item">
              <view class="form-item-label">姓名</view>
              <view class="form-item-value">
                <input
                  placeholder="请输入姓名"
                  maxlength="10"
                  placeholder-class="placeholder"
                  v-model="form.name"
                  @blur="nameBlur"
                />
              </view>
            </view>
            <view
              class="form-item-line"
              v-if="isVisible('age') || isVisible('sex')"
            ></view>
          </template>

          <template v-if="isVisible('age')">
            <view class="identity-form-item form-item">
              <view class="form-item-label">年龄</view>
              <view class="form-item-value">
                <input
                  placeholder="请填写年龄"
                  maxlength="3"
                  placeholder-class="placeholder"
                  v-model="form.age"
                  @blur="ageBlur"
                />
              </view>
            </view>
            <view class="form-item-line" v-if="isVisible('sex')"></view>
          </template>

          <template v-if="isVisible('sex')">
            <view class="identity-form-item form-item">
              <view class="form-item-label">性别</view>
              <view class="form-item-value">
                <view class="radio">
                  <view class="radio-item" :class="{ selected: form.sex === 0 }" @click="clickSex(0)">男</view>
                  <view class="radio-item" :class="{ selected: form.sex === 1 }" @click="clickSex(1)">女</view>
                </view>
              </view>
            </view>
          </template>
        </view>
      </view>

      <view
        class="qualification card"
        :class="{ activeForm: activeForm === 'qualification' }"
        @click="setActiveForm('qualification')"
        v-if="isVisible('sesameId') || isVisible('isOverdue') || isVisible('otherAptitude')"
      >
        <view class="card-header">
          <view class="card-header-label">
            <image
              class="card-header-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/d20f671665024bfba9549e62a25b9f06.png"
            ></image>
            <view>资质信息</view>
          </view>
        </view>

        <view class="qualification-form" v-if="activeForm === 'qualification'">
          <template v-if="isVisible('age')">
            <view class="identity-form-item form-item">
              <view class="form-item-label">年龄</view>
              <view class="form-item-value">
                <input
                  placeholder="请填写年龄"
                  maxlength="3"
                  placeholder-class="placeholder"
                  v-model="form.age"
                  @blur="ageBlur"
                />
              </view>
            </view>
            <view
              class="form-item-line"
              v-if="isVisible('sesameId') || isVisible('isOverdue')"
            ></view>
          </template>

          <template v-if="isVisible('sesameId')">
            <view class="qualification-form-item form-item" @click="activeFormItem = 'sesameScore'">
              <view class="form-item-label">芝麻分</view>
              <view class="form-item-value">
                <input
                  disabled
                  style="pointer-events: none"
                  :value="
                    sesameScoreOptions[form.sesameScoreIndex]
                      ? sesameScoreOptions[form.sesameScoreIndex].label
                      : ''
                  "
                  placeholder="请选择芝麻分"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>
            <view class="form-item-line" v-if="isVisible('isOverdue')"></view>
            <view class="radio-group" v-if="activeFormItem === 'sesameScore'">
              <view
                class="radio"
                v-for="(item, index) in sesameScoreOptions"
                :key="index"
                :class="{ selected: index === form.sesameScoreIndex }"
                @click="clickSesameScore(index)"
              >
                {{ item.label }}

                <image
                  v-if="index === form.sesameScoreIndex"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/993e727c1b07455b8eb4d11b1108eb2c.png"
                ></image>
              </view>
            </view>
          </template>

          <template v-if="isVisible('isOverdue')">
            <view class="qualification-form-item form-item" @click="activeFormItem = 'credit'">
              <view class="form-item-label">信用情况</view>
              <view class="form-item-value">
                <input
                  disabled
                  style="pointer-events: none"
                  :value="
                    creditOptions[form.creditIndex] ? creditOptions[form.creditIndex].label : ''
                  "
                  placeholder="请选择信用情况"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>
            <view class="form-item-line" v-if="isVisible('otherAptitude')"></view>
            <view class="radio-group" v-if="activeFormItem === 'credit'">
              <view
                class="radio"
                v-for="(item, index) in creditOptions"
                :key="index"
                :class="{ selected: index === form.creditIndex }"
                @click="clickCredit(index)"
              >
                {{ item.label }}

                <image
                  v-if="index === form.creditIndex"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/993e727c1b07455b8eb4d11b1108eb2c.png"
                ></image>
              </view>
            </view>
          </template>

          <template v-if="isVisible('otherAptitude')">
            <view class="qualification-form-item form-item" @click="activeFormItem = 'other'">
              <view class="form-item-label">
                其他资产
                <text class="form-item-desc">*至少选择一项</text>
              </view>
              <view class="form-item-value">
                <input
                  style="pointer-events: none"
                  disabled
                  placeholder="选择额度可提额"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>

            <template v-if="activeFormItem === 'other'">
              <view class="form-item-line"></view>

              <view class="radio-group" id="other-form-item">
                <view
                  class="radio"
                  v-for="(item, index) in otherOptions"
                  :key="index"
                  :class="{ selected: form[item.key] }"
                  @click="clickOther(item)"
                >
                  {{ item.label }}
                  <image
                    v-if="form[item.key]"
                    class="radio-selected-icon"
                    src="https://cdn.oss-unos.hmctec.cn/common/path/993e727c1b07455b8eb4d11b1108eb2c.png"
                  ></image>

                  <image
                    v-if="item.highQuality"
                    class="radio-high-quality-icon"
                    src="https://cdn.oss-unos.hmctec.cn/common/path/625eaa5e5b534bf79de4c2d518408a19.png"
                  ></image>
                </view>
              </view>
            </template>
          </template>
        </view>
      </view>


    </scroll-view>
    <view class="page-footer">
      <view class="submit-btn" @click="clickSubmit">提交申请</view>
    </view>


    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import LockNativeBack from '@/utils/lock-native-back'
import { fetchAptitudeRestock, matchingOnlineProduct, reportUV } from '@/apis/common'
import { register } from '@/apis/common-2'
import { saveAptitude } from '@/apis/user'
import { encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'

import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { isInWechatMiniProgram } from '@/utils/user-agent'
import { saveAppletOpenRecord } from '@/apis/common-3'

export default {
  name: 'template-v153',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        templateVersion: 'v153',
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        cityName: '',
        cityCode: '',
        name: '',
        age: '',
        sex: '',
        channelId: '',
        consumerId: '',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false,
        isProvident: false,
        isHouse: false,
        isVehicle: false,
        isSocial: false,
        isInsure: false,
        isBusiness: false,
        clearAll: false
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      otherOptions: [
        {
          key: 'isProvident',
          value: true,
          label: '有公积金',
          highQuality: true
        },
        {
          key: 'isHouse',
          value: true,
          label: '有房'
        },
        {
          key: 'isVehicle',
          value: true,
          label: '有车'
        },
        {
          key: 'isSocial',
          value: true,
          label: '有社保'
        },
        {
          key: 'isInsure',
          value: true,
          label: '有保险单'
        },
        {
          key: 'isBusiness',
          value: true,
          label: '有营业执照'
        },
        {
          key: 'clearAll',
          value: true,
          label: '以上都没有'
        }
      ],
      monthlyPay: '',
      // 默认展示身份信息
      activeForm: 'identity',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 115,
          label: '700分以上'
        },
        {
          value: 113,
          label: '650-700分'
        },
        {
          value: 112,
          label: '600-650分'
        },
        {
          value: 110,
          label: '600分以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],

      scrollIntoView: '',
      lockBack: null,
      visibleFields: [],
      areaData: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  async onLoad({ i, u }) {
    setFlowNodes(['wechat', 'overloan'])
    this.$u.vuex('vuex_invite', i)
    this.$u.vuex('vuex_uid', u)



    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()

    await this.register(u, i)
    await this.fetchVisibleFields()
    if (this.form.channelId) {
      this.fetchAreaData()
      this.fetchAddressByIP()
    }
  },

  methods: {
    isVisible(fieldName) {
      if (!this.visibleFields || this.visibleFields.length === 0) {
        return true // 默认情况下全部显示
      }
      return this.visibleFields.includes(fieldName)
    },

    /**
     * @description 获取需要动态展示的资质字段
     * fetchAptitudeRestock 接口可返回的字段有: city,name,age,sex,sesameId,isOverdue,otherAptitude
     */
    async fetchVisibleFields() {
      if (!this.form.channelId) {
        this.visibleFields = []
        return
      }
      try {
        const res = await fetchAptitudeRestock({ channelId: this.form.channelId })
        if (res.code == 200 && Array.isArray(res.data)) {
          this.visibleFields = res.data
        } else {
          this.visibleFields = []
        }
      } catch (error) {
        this.visibleFields = []
      }
    },

    async fetchAreaData() {
      const { fetchAreaData } = await import('@/apis/common-2')
      const res = await fetchAreaData()
      const area = res[1].data
      this.areaData = [area, area[0].citys]
    },

    async fetchAddressByIP() {
      const { getIpArea } = await import('@/apis/common-2')
      const res = await getIpArea()
      if (res.data) {
        this.form.cityName = res.data.cityName
        this.form.cityCode = res.data.cityCode
      }
    },

    validateCity() {
      if (!this.isVisible('city')) return true

      if (!this.form.cityCode) {
        uni.showToast({
          title: '请选择城市',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateName() {
      if (!this.isVisible('name')) return true

      this.form.name = this.form.name ? this.form.name.trim() : ''

      if (!this.form.name) {
        uni.showToast({
          title: '请填写姓名',
          icon: 'none'
        })

        return false
      }

      if (!this.$u.test.chinese(this.form.name)) {
        uni.showToast({
          title: '姓名格式错误',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateSex() {
      if (!this.isVisible('sex')) return true

      if (!this.form.sex && typeof this.form.sex !== 'number') {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateOther() {
      if (!this.isVisible('otherAptitude')) return true

      if (!this.form.clearAll) {
        const keys = ['isProvident', 'isHouse', 'isVehicle', 'isSocial', 'isInsure', 'isBusiness']
        // 至少选择一项
        const selectedKeys = keys.filter((key) => this.form[key])
        if (selectedKeys.length === 0) {
          uni.showToast({
            title: '请选择其他资质',
            icon: 'none'
          })

          return false
        }

        return true
      }

      return true
    },

    validateIdentityForm() {
      return this.validateName() && this.validateAge() && this.validateSex()
    },

    regionColChange({ detail }) {
      const { column, value: provinceIndex } = detail
      if (column === 0) {
        const citys = this.areaData[0][provinceIndex].citys
        this.areaData[1] = citys
      }
    },

    regionChange({ detail }) {
      const { value } = detail
      const [, cityIndex] = value
      this.form.cityName = this.areaData[1][cityIndex].name
      this.form.cityCode = this.areaData[1][cityIndex].code
    },

    nameBlur() {
      this.validateName()
    },

    clickSex(value) {
      this.form.sex = value

      const namePass = this.form.name && this.validateName()
      const agePass = this.form.age && this.validateAge()
      if (namePass && agePass) {
        setTimeout(() => {
          this.activeForm = 'qualification'
        }, 0)
      }
    },

    clickOther(item) {
      if (item.key === 'clearAll') {
        this.otherOptions.forEach((option) => {
          this.form[option.key] = false
        })
        this.form.clearAll = true
      } else {
        this.form[item.key] = !this.form[item.key]
        this.form.clearAll = false
      }
    },

    async register(uid, invite) {
      const res = await register({ uid, invite })
      if (res.code == 200) {
        this.form.consumerId = res.data.consumerId
        this.form.channelId = res.data.channelId

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
        this.$u.vuex('vuex_channelId', this.form.channelId)

        await reportUV({
          channelId: this.form.channelId
        })

        if (isInWechatMiniProgram()) {
          await saveAppletOpenRecord({
            channelId: this.form.channelId,
            appUserId: this.form.consumerId
          })
        }
      }
    },



    validateSesameScore() {
      if (!this.isVisible('sesameId')) return true

      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateCredit() {
      if (!this.isVisible('isOverdue')) return true

      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateAge() {
      if (!this.isVisible('age')) return true

      this.form.age = this.form.age ? this.form.age.trim() : ''

      if (!this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(this.form.age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      if (this.form.age < 22 || this.form.age > 55) {
        uni.showToast({
          title: '仅支持 22-55 岁的用户',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateQualificationForm() {
      return this.validateSesameScore() && this.validateCredit() && this.validateOther()
    },

    setActiveForm(formName) {
      if (formName === 'qualification') {
        if (!this.validateIdentityForm()) {
          return
        }
      }

      this.activeForm = formName
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index
      this.activeFormItem = 'credit'
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
      this.activeFormItem = 'other'
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (
        !this.validateCity() ||
        !this.validateIdentityForm() ||
        !this.validateQualificationForm()
      ) {
        return
      }

      const saveData = {
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      }

      if (this.isVisible('city')) {
        saveData.cityCode = this.form.cityCode
      }

      if (this.isVisible('name')) {
        saveData.name = this.form.name
      }

      if (this.isVisible('age')) {
        saveData.age = this.form.age
      }

      if (this.isVisible('sex')) {
        saveData.sex = this.form.sex
      }

      if (this.isVisible('sesameId')) {
        saveData.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      }

      if (this.isVisible('isOverdue')) {
        saveData.isOverdue = this.form.isOverdue
      }

      if (this.isVisible('otherAptitude')) {
        saveData.isProvident = this.form.isProvident
        saveData.isHouse = this.form.isHouse
        saveData.isVehicle = this.form.isVehicle
        saveData.isSocial = this.form.isSocial
        saveData.isInsure = this.form.isInsure
        saveData.isBusiness = this.form.isBusiness
      }

      try {
        const res = await saveAptitude(saveData)
        if (res.code === 200) {
          // 保存成功，继续下一步流程
          this.navigateToNextFlow()
        } else {
          // 保存失败，显示错误信息
          uni.showToast({
            title: res.msg || '保存失败，请重试',
            icon: 'none'
          })
        }
      } catch (error) {
        // 网络错误或其他异常
        console.error('保存资质信息失败:', error)
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        })
      }
    },



    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v153/qw/index',
        overloan: '/extreme/v153/applyOther/index',
        end: '/extreme/v153/download/index'
      }

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    getUserParams() {
      const params = {}
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      params.isOverdue = this.form.isOverdue
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.monthIndex = this.form.monthIndex

      return params
    },



    ageBlur() {
      this.validateAge()
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  height: 100vh;
  background: #f6f6f8;

  .page-content {
    flex: 1;
    overflow: hidden auto;
    position: relative;
    padding-bottom: 400rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/2d3be2970b47431fbf63b72e18e05c18.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;



    .submit-btn {
      padding: 21rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 56rpx;
      text-align: center;
    }
  }
}

.header {
  position: relative;
  padding: 0 0 45rpx 30rpx;

  .header-title {
    .header-title-text {
      margin-bottom: 16rpx;
      font-weight: 700;
      font-size: 38rpx;
      color: #333333;
      line-height: 50rpx;
    }

    .header-title-subtext {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;
    }
  }

  .tag {
    position: absolute;
    top: 50rpx;
    right: 0;
    padding: 6rpx 7rpx 6rpx 10rpx;
    background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
    border-radius: 20rpx 0rpx 0rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #5a352f;
    line-height: 16rpx;
    display: flex;
    align-items: center;

    .tag-number {
      font-weight: 400;
      font-size: 24rpx;
      color: #5a352f;
      line-height: 28rpx;
    }

    .tag-gap-text {
      margin-left: 3rpx;
      margin-right: 6rpx;
    }
  }
}

.city {
  position: relative;
  margin: 0 30rpx;
  background: #ffffff;
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  padding: 29rpx 32rpx;

  .city-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .city-label {
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 52rpx;
    }

    .city-value {
      display: flex;
      align-items: center;

      .city-icon {
        margin-right: 10rpx;
        width: 40rpx;
        height: 40rpx;
      }

      .city-name {
        max-width: 8em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 30rpx;
        font-weight: 400;
        font-size: 36rpx;
        color: #333333;
        line-height: 52rpx;
      }
    }
  }

  .city-tips {
    margin-top: 20rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #a2a3a5;
    line-height: 32rpx;
  }
}

.identity {
  .identity-form {
    .identity-form-item {
      .form-item-value {
        font-weight: 400;
        font-size: 32rpx;
        color: #999999;
        line-height: 46rpx;
        text-align: right;

        .radio {
          display: flex;
          align-items: center;
          gap: 12rpx;

          .radio-item {
            background: #f6f6f8;
            border-radius: 10rpx 10rpx 10rpx 10rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #999999;
            line-height: 34rpx;
            padding: 7rpx 36rpx;
            border: 1rpx solid #f6f6f8;

            &.selected {
              background: #ebf3ff;
              border-color: $color-primary;
              color: $color-primary;
            }
          }
        }
      }
    }
  }
}

.card {
  position: relative;
  margin: 40rpx 30rpx 0;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  padding-bottom: 20rpx;
  border: 1rpx solid #ffffff;

  &.activeForm {
    border: 1rpx solid $color-primary;
  }

  .card-header {
    padding: 30rpx 32rpx 10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-header-label {
    display: flex;
    gap: 12rpx;
    align-items: center;
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 50rpx;
  }

  .card-header-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.form-item-line {
  margin-left: 32rpx;
  height: 1rpx;
  background: #e2e2e2;
}

.form-item {
  padding: 25rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item-label {
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
}

.form-item-value {
  display: flex;
  align-items: center;
  gap: 22rpx;
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
  text-align: right;
}

.qualification {
  .qualification-form {
    .qualification-form-item {
      .form-item-label {
        width: 380rpx;

        .form-item-desc {
          margin-left: 10rpx;
          color: #f01515;
          font-size: 24rpx;
        }
      }

      .form-item-value {
        display: flex;
        align-items: center;
        gap: 22rpx;
        font-weight: 400;
        font-size: 32rpx;
        color: #999999;
        line-height: 46rpx;
        text-align: right;
      }
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15rpx;
      padding: 20rpx 30rpx;

      .radio {
        overflow: hidden;
        position: relative;
        width: 198rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f6f6f8;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;
        text-align: center;
        border: 1rpx solid #f6f6f8;

        &.selected {
          border-color: $color-primary;
          background-color: #ebf3ff;
          color: $color-primary;
        }

        .radio-selected-icon {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 34rpx;
          height: 34rpx;
        }

        .radio-high-quality-icon {
          position: absolute;
          right: 0;
          top: 0;
          width: 52rpx;
          height: 23rpx;
        }
      }
    }
  }
}

.placeholder {
  font-weight: normal;
  font-size: 32rpx;
  color: #a2a3a5;
  line-height: 40rpx;
  font-style: normal;
  text-transform: none;
}


</style>
