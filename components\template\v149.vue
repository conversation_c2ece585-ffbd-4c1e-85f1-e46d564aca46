<template>
  <view class="page-container">
    <view class="tips-container">
      <view class="tips-content">
        <image src="https://cdn.oss-unos.hmctec.cn/common/path/194cabc3554e4eceb0e901fd4a354983.png" mode="scaleToFill"
          class="tips-img" />

        <uni-notice-bar background-color="transparent" color="#268fb2" single scrollable class="tips-text"
          text="温馨提示:所有贷款在未放成功放款前，绝不收取任何费用" />
      </view>
    </view>

    <view class="page-content">
      <view class="amount-container">
        <view class="amount-title">最高可借200000(元)</view>
        <view class="amount-input-container">
          <view class="amount-input">
            <view class="amount-input-prefix">￥</view>
            <input class="amount-input-value" v-model="amount" type="number" @blur="amountInputBlur" />
          </view>
          <view class="amount-input-suffix">
            <view class="amount-input-suffix-text">(金额可修改)</view>
            <image src="https://cdn.oss-unos.hmctec.cn/common/path/9884c7d229324121a80b0e86a5d6d099.png"
              mode="scaleToFill" class="clear-icon" @click="clearAmountClick" />
          </view>
        </view>
      </view>

      <view class="rate-container">参考年化利率7.2%起,1000元1天仅需0.3元</view>

      <view class="period-container">
        <view class="period-title">还款期数</view>
        <view class="period-content">
          <view class="period-item" v-for="item in periodList" :key="item" :class="{ selected: item === period }"
            @click="periodClick(item)">{{ item }}期</view>
        </view>
      </view>

      <view class="plan-container">
        <view class="plan-item">
          <view class="plan-item-title">还款计划</view>
          <view class="plan-item-content">应还{{ monthlyPay }}元</view>
        </view>
        <view class="plan-item">
          <view class="plan-item-title">还款方式</view>
          <view class="plan-item-content">
            <text class="plan-item-content-desc">*按日计息,提前还0手续费</text>
            <text>随借随还</text>
          </view>
        </view>
        <view class="plan-item">
          <view class="plan-item-title">到账时间</view>
          <view class="plan-item-content">最快1小时</view>
        </view>
      </view>

      <view class="tips2-container">*具体额度利率以实际审批为准</view>

      <view class="phone-container">
        <view class="phone-input">
          <image class="phone-input-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/36bcffee81d3457e84109398dce42214.png" mode="scaleToFill" />
          <input type="number" placeholder="请输入手机号(已加密）" v-model="phone" @blur="phoneInputBlur" maxlength="11" />
        </view>

        <!-- 人机验证组件 -->
        <aliyun-captcha
          v-show="shouldShowCaptcha"
          @success="onCaptchaSuccess"
          @fail="onCaptchaFail"
          @error="onCaptchaError"
        >
          <view class="phone-btn">
            <text>领取我的额度</text>
          </view>
        </aliyun-captcha>

        <!-- 普通按钮 -->
        <view v-show="!shouldShowCaptcha" class="phone-btn" @click="phoneBtnClick">
          领取我的额度
        </view>
      </view>

      <view class="declare-container">
        <view>郑重声明</view>
        <view>本平台只提供贷款咨询和推荐服务，
          放款由银行或金融机构进行，所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全</view>
        <view>请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。证您的资金安全</view>
      </view>
    </view>


  </view>
</template>

<script>
import { reportUV, getDcProductLink, saveAptitude, getAptitudeLink } from '@/apis/common'
import { behaviorVerify } from '@/apis/user'
import { encryptByDES } from '@/utils/encrypt'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import AliyunCaptcha from '@/components/captcha/aliyun-captcha.vue'

export default {
  name: 'v149',
  components: {
    AliyunCaptcha
  },
  data() {
    return {
      amount: 200000,
      periodList: [36, 24, 12, 6, 3],
      period: 36,
      phone: '',
      monthlyPay: '',
      captchaVerifyParam: null
    }
  },

  computed: {
    shouldShowCaptcha() {
      return this.$u.test.mobile(this.phone);
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },



  mounted() {
    if (this.channelId) {
      // 上报UV
      reportUV({ channelId: this.channelId })
    }
    this.computedMonthPay()
    uni.setNavigationBarTitle({
      title: '多多钱包'
    })
  },
  methods: {
    amountInputBlur() {
      if (isNaN(this.amount)) {
        this.amount = 200000
        return
      }

      if (this.amount < 50000) {
        this.amount = 50000
        return
      }

      if (this.amount > 200000) {
        this.amount = 200000
        return
      }

      this.computedMonthPay()
    },

    clearAmountClick() {
      this.amount = 200000
      this.computedMonthPay()
    },

    periodClick(item) {
      this.period = item
      this.computedMonthPay()
    },

    phoneInputBlur() {
      if (!this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },

    phoneBtnClick() {
      if (!this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },

    // 人机验证成功回调
    async onCaptchaSuccess(captchaVerifyParam) {
      this.captchaVerifyParam = captchaVerifyParam

      // 显示验证成功提示
      uni.showToast({
        title: '验证成功',
        icon: 'success',
        duration: 1500
      })

      // 先调用行为验证接口进行后端校验
      try {
        uni.showLoading({
          title: '验证中...',
          mask: true
        })

        const verifyRes = await behaviorVerify({
          phone: this.phone,
          template: 'v149',
          channelId: this.channelId,
          captchaVerifyParam
        })        

        uni.hideLoading()

        if (verifyRes.code !== 200 || verifyRes.data !== true) {
          uni.showToast({
            title: '验证失败',
            icon: 'none'
          })
          this.resetCaptcha()
          return
        }

        // 验证通过后，直接进行登录
        this.login()
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '验证失败，请重试',
          icon: 'none'
        })
        this.resetCaptcha()
      }
    },

    // 人机验证失败回调
    onCaptchaFail(error) {
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 人机验证错误回调
    onCaptchaError(error) {
      uni.showToast({
        title: '验证组件加载失败',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 重置验证状态
    resetCaptcha() {
      this.captchaVerifyParam = null;
    },

    computedMonthPay() {
      let price = Number(this.amount)
      let mLatte = (price * 12) / 100 / 12
      const month = this.period

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },



    async getDcProductLink(consumerId) {
      uni.showLoading({
        title: '加载中',
        mask: true
      })

      try {
        const res = await getDcProductLink({ consumerId })
        return res.data || ''
      } catch (error) {
        return ''
      } finally {
        uni.hideLoading()
      }
    },

    async getAptitudeLink(consumerId, channelId) {
      try {
        const res = await getAptitudeLink({ consumerId, channelId })
        return res.data || ''
      } catch (error) {
        return ''
      }
    },

    async login() {
      try {
        const params = {
          phone: this.phone,
          channelId: this.channelId,
          demandAmount: this.amount,
          deviceType: uni.getSystemInfoSync().platform,
          phoneBlack: getBlackPhone()
        }

        const res = await saveAptitude(params)

        if (res.code != 200) {
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          })
          return
        }

        // 登录成功后，记录手机号到黑名单
        setBlackPhone(params.phone)
        this.$u.vuex('vuex_phone', params.phone)
        this.$u.vuex('vuex_consumerId', res.data)

        const urlParam = {
          consumerId: res.data,
          phone: this.phone,
          demandAmount: this.amount,
          channelId: this.channelId,
          monthIndex: this.periodList.indexOf(this.period)
        }

        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))

        // 先获取联登链接
        const aptitudeLink = await this.getAptitudeLink(urlParam.consumerId, this.channelId)

        if (aptitudeLink) {
          window.location.href = aptitudeLink
          return
        }
        
        const link = await this.getDcProductLink(urlParam.consumerId)
        this.$u.vuex('vuex_overloanWebview.url', link)

        if (link) {
          uni.navigateTo({
            url: `/extreme/v149/overloanWebview/index?param=${urlParamString}`
          })
        } else {
          uni.navigateTo({
            url: `/extreme/v149/wechatOfficialAccount/index?param=${urlParamString}`
          })
        }
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f3f4f8;
  display: flex;
  flex-direction: column;
}

.tips-container {
  padding: 20rpx;

  .tips-content {
    display: flex;
    align-items: center;
    gap: 10rpx;
    background: #e7f6fe;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 15rpx;

    .tips-img {
      width: 40rpx;
      height: 40rpx;
    }

    .tips-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #268fb2;
      line-height: 40rpx;
      margin: 0;
      padding: 0;
    }
  }
}

.page-content {
  flex: 1;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 45rpx 30rpx;
}

.amount-container {
  .amount-title {
    font-weight: normal;
    font-size: 28rpx;
    color: #171a1d;
    line-height: 39rpx;
    margin-bottom: 15rpx;
  }

  .amount-input-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1rpx solid #d8d8d8;

    .amount-input {
      display: flex;
      align-items: center;

      .amount-input-prefix {
        font-weight: 700;
        font-size: 48rpx;
        color: #333333;
        line-height: 67rpx;
      }

      .amount-input-value {
        font-family: D-DIN, D-DIN;
        font-weight: 700;
        font-size: 80rpx;
        color: #333333;
        line-height: 112rpx;
        width: 300rpx;
      }
    }
  }

  .amount-input-suffix {
    display: flex;
    align-items: center;
    gap: 15rpx;

    .amount-input-suffix-text {
      font-weight: normal;
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
      flex-shrink: 0;
    }

    .clear-icon {
      width: 30rpx;
      height: 30rpx;
      flex-shrink: 0;
    }
  }
}

.rate-container {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 36rpx;
  margin-top: 15rpx;
  margin-bottom: 35rpx;
}

.period-container {
  .period-title {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 41rpx;
    margin-bottom: 25rpx;
  }

  .period-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30rpx;

    .period-item {
      width: 112rpx;
      height: 60rpx;
      background: #f7f7f7;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Arial, Arial;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      &.selected {
        background: #33adfd;
        color: #ffffff;
      }
    }
  }
}

.plan-container {
  margin-top: 17rpx;

  .plan-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18rpx 0;

    .plan-item-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
    }

    .plan-item-content {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 42rpx;

      .plan-item-content-desc {
        margin-right: 5rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 21rpx;
      }
    }
  }
}

.tips2-container {
  margin-bottom: 70rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 35rpx;
}

.phone-container {
  margin-bottom: 50rpx;

  .phone-input {
    margin-bottom: 30rpx;
    background: #ffffff;
    border-radius: 723rpx 723rpx 723rpx 723rpx;
    border: 2rpx solid #33adfd;
    padding: 28rpx 38rpx;
    display: flex;
    align-items: center;
    gap: 17rpx;

    .phone-input-icon {
      width: 50rpx;
      height: 45rpx;
    }

    input {
      font-weight: 400;
      font-size: 36rpx;
      line-height: 41rpx;
    }
  }

  .phone-btn {
    padding: 22rpx;
    background: linear-gradient(270deg, #34b0fd 0%, #277bff 100%);
    border-radius: 546rpx 546rpx 546rpx 546rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 58rpx;
  }
}

.declare-container {
  padding: 0 60rpx;
  font-weight: normal;
  font-size: 24rpx;
  color: #939393;
  line-height: 34rpx;
  text-align: center;
}


</style>
